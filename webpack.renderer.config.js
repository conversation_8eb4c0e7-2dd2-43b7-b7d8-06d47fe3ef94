const path = require('path');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  target: 'electron-renderer',
  entry: './src/renderer-router.tsx',
  
  // Configure webpack dev server with reliable settings
  devServer: {
    port: 3002,
    hot: true,
    liveReload: true,
    historyApiFallback: true,
    compress: true,
    static: false,
    client: {
      logging: 'warn',
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    // Disable host checking for electron
    allowedHosts: 'all',
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  node: {
    __dirname: true,  // Provide polyfill for __dirname
    __filename: true, // Provide polyfill for __filename
    global: true,     // Provide polyfill for global
  },
  mode: isProduction ? 'production' : 'development',
  devtool: isProduction ? 'source-map' : 'eval-cheap-module-source-map',
  
  module: {
    rules: [
      ...require('./webpack.rules'),
      // Enhanced CSS processing with PostCSS and TailwindCSS
      {
        test: /\.css$/,
        use: [
          { 
            loader: 'style-loader',
            options: {
              // Insert styles at the top of <head> for better performance
              insert: 'head',
              injectType: 'singletonStyleTag',
            },
          },
          { 
            loader: 'css-loader',
            options: {
              importLoaders: 1,
              sourceMap: !isProduction,
              modules: {
                auto: true, // Enable CSS modules for files with .module.css
                localIdentName: isProduction 
                  ? '[hash:base64:8]' 
                  : '[name]__[local]--[hash:base64:5]',
              },
            },
          },
          { 
            loader: 'postcss-loader',
            options: {
              sourceMap: !isProduction,
              postcssOptions: {
                config: path.resolve(__dirname, 'postcss.config.js'),
              },
            },
          },
        ],
      },
      // SCSS/SASS support (if needed in the future)
      {
        test: /\.s[ac]ss$/i,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,
              sourceMap: !isProduction,
            },
          },
          'postcss-loader',
          'sass-loader',
        ],
      },
    ],
  },
  
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css', '.scss', '.sass', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/services': path.resolve(__dirname, 'src/services'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/shared': path.resolve(__dirname, 'src/shared'),
      '@/handlers': path.resolve(__dirname, 'src/handlers'),
      '@/repositories': path.resolve(__dirname, 'src/repositories'),
      '@/adapters': path.resolve(__dirname, 'src/adapters'),
      '@/styles': path.resolve(__dirname, 'src/styles'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/frontend': path.resolve(__dirname, 'src/frontend'),
      '@/backend': path.resolve(__dirname, 'src/backend'),
    },
    fallback: {
      'events': require.resolve('events/'),
      'util': require.resolve('util/'),
      'path': require.resolve('path-browserify'),
      'fs': false,
      'crypto': require.resolve('crypto-browserify'),
      'stream': require.resolve('stream-browserify'),
      'buffer': require.resolve('buffer'),
      'process': require.resolve('process/browser'),
    },
  },
  
  plugins: [
    new ForkTsCheckerWebpackPlugin({
      typescript: {
        configFile: path.resolve(__dirname, 'tsconfig.json'),
        diagnosticOptions: {
          semantic: true,
          syntactic: true,
        },
        mode: 'write-references',
      },
      logger: 'webpack-infrastructure',
      async: !isProduction, // Async in development for faster builds
    }),
    
    // Define global variables to prevent ReferenceError
    new (require('webpack')).DefinePlugin({
      '__dirname': JSON.stringify(__dirname),
      '__filename': JSON.stringify(__filename),
      'global': 'globalThis',
    }),

    // Provide polyfills for Node.js modules
    new (require('webpack')).ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),
  ],
  
  optimization: {
    // Minimal optimization for faster dev builds
    minimize: false,
    splitChunks: isProduction ? {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    } : false,
  },
  
  performance: {
    hints: isProduction ? 'warning' : false,
    maxEntrypointSize: 1024000, // 1MB
    maxAssetSize: 512000, // 512KB
  },
  
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    chunkModules: false,
    timings: true,
    builtAt: true,
  },
  

  
  // Simple cache configuration for development
  cache: isProduction ? {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
  } : false,
};