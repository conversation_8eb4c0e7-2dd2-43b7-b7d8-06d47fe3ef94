const path = require('path');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  target: 'electron-renderer',
  entry: './src/renderer-router.tsx',
  
  // Configure webpack dev server
  devServer: {
    hot: true,
    liveReload: true,
    port: 3001,
    historyApiFallback: true,
  },
  node: {
    __dirname: true,  // Provide polyfill for __dirname
    __filename: true, // Provide polyfill for __filename
    global: true,     // Provide polyfill for global
  },
  mode: isProduction ? 'production' : 'development',
  devtool: isProduction ? 'source-map' : 'eval-cheap-module-source-map',
  
  module: {
    rules: [
      ...require('./webpack.rules'),
      // Enhanced CSS processing with PostCSS and TailwindCSS
      {
        test: /\.css$/,
        use: [
          { 
            loader: 'style-loader',
            options: {
              // Insert styles at the top of <head> for better performance
              insert: 'head',
              injectType: 'singletonStyleTag',
            },
          },
          { 
            loader: 'css-loader',
            options: {
              importLoaders: 1,
              sourceMap: !isProduction,
              modules: {
                auto: true, // Enable CSS modules for files with .module.css
                localIdentName: isProduction 
                  ? '[hash:base64:8]' 
                  : '[name]__[local]--[hash:base64:5]',
              },
            },
          },
          { 
            loader: 'postcss-loader',
            options: {
              sourceMap: !isProduction,
              postcssOptions: {
                config: path.resolve(__dirname, 'postcss.config.js'),
              },
            },
          },
        ],
      },
      // SCSS/SASS support (if needed in the future)
      {
        test: /\.s[ac]ss$/i,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,
              sourceMap: !isProduction,
            },
          },
          'postcss-loader',
          'sass-loader',
        ],
      },
    ],
  },
  
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css', '.scss', '.sass', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/services': path.resolve(__dirname, 'src/services'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/shared': path.resolve(__dirname, 'src/shared'),
      '@/handlers': path.resolve(__dirname, 'src/handlers'),
      '@/repositories': path.resolve(__dirname, 'src/repositories'),
      '@/adapters': path.resolve(__dirname, 'src/adapters'),
      '@/styles': path.resolve(__dirname, 'src/styles'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/frontend': path.resolve(__dirname, 'src/frontend'),
      '@/backend': path.resolve(__dirname, 'src/backend'),
    },
  },
  
  plugins: [
    new ForkTsCheckerWebpackPlugin({
      typescript: {
        configFile: path.resolve(__dirname, 'tsconfig.json'),
        diagnosticOptions: {
          semantic: true,
          syntactic: true,
        },
        mode: 'write-references',
      },
      logger: 'webpack-infrastructure',
      async: !isProduction, // Async in development for faster builds
    }),
    
    // Define global variables to prevent ReferenceError
    new (require('webpack')).DefinePlugin({
      '__dirname': JSON.stringify(__dirname),
      '__filename': JSON.stringify(__filename),
      'global': 'globalThis',
    }),
  ],
  
  optimization: {
    // Simple optimization for development
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
  
  performance: {
    hints: isProduction ? 'warning' : false,
    maxEntrypointSize: 1024000, // 1MB
    maxAssetSize: 512000, // 512KB
  },
  
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    chunkModules: false,
    timings: true,
    builtAt: true,
  },
  
  // Externalize Node.js modules to prevent require errors
  externals: {
    'events': 'commonjs events',
    'util': 'commonjs util',
    'path': 'commonjs path',
    'fs': 'commonjs fs',
  },
  
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
    // Optimize cache for faster rebuilds
    compression: 'gzip',
    hashAlgorithm: 'xxhash64',
  },
  
  // Development server optimizations
  ...(isProduction ? {} : {
    snapshot: {
      managedPaths: [path.resolve(__dirname, 'node_modules')],
      immutablePaths: [],
      buildDependencies: {
        hash: true,
        timestamp: true,
      },
      module: {
        timestamp: true,
      },
      resolve: {
        timestamp: true,
      },
      resolveBuildDependencies: {
        hash: true,
        timestamp: true,
      },
    },
  }),
};