import React from 'react';
import ReactDOM from 'react-dom/client';
import { App } from './App';
import './styles/globals.css';

console.log('🔍 DEBUGGING: Router renderer script started!');
console.log('🔍 DEBUGGING: Document ready state:', document.readyState);

const rootElement = document.getElementById('root');
console.log('🔍 DEBUGGING: Root element found:', !!rootElement);

// Function to hide loading screen
const hideLoadingScreen = () => {
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) {
    loadingScreen.style.opacity = '0';
    loadingScreen.style.transition = 'opacity 0.3s ease-out';
    setTimeout(() => {
      loadingScreen.style.display = 'none';
    }, 300);
  }
};

if (rootElement) {
  console.log('🔍 DEBUGGING: Creating React root with router...');
  const root = ReactDOM.createRoot(rootElement);

  console.log('🔍 DEBUGGING: About to render App...');
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  );
  console.log('🔍 DEBUGGING: App rendered successfully!');

  // Hide loading screen after React renders
  setTimeout(hideLoadingScreen, 500);
} else {
  console.error('❌ DEBUGGING: Root element not found!');
}
